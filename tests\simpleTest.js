import fs from 'fs';
import path from 'path';

/**
 * Teste simples para identificar problemas básicos no bot
 */
class SimpleBotTest {
    constructor() {
        this.errors = [];
        this.warnings = [];
    }

    log(message) {
        console.log(`[TEST] ${message}`);
    }

    error(message) {
        console.error(`[ERROR] ${message}`);
        this.errors.push(message);
    }

    warn(message) {
        console.warn(`[WARN] ${message}`);
        this.warnings.push(message);
    }

    async runBasicTests() {
        this.log('🧪 Iniciando testes básicos do bot...');
        
        await this.testFileStructure();
        await this.testPackageJson();
        await this.testEnvironmentFile();
        await this.testModels();
        await this.testCommands();
        await this.testHandlers();
        await this.testUtils();
        
        this.generateReport();
    }

    async testFileStructure() {
        this.log('📁 Verificando estrutura de arquivos...');
        
        const requiredFiles = [
            'index.js',
            'package.json',
            '.env.example',
            'src/database/connection.js',
            'src/config/constants.js'
        ];
        
        const requiredDirs = [
            'src',
            'src/commands',
            'src/commands/general',
            'src/commands/store',
            'src/models',
            'src/handlers',
            'src/utils',
            'src/events'
        ];
        
        for (const file of requiredFiles) {
            if (!fs.existsSync(file)) {
                this.error(`Arquivo obrigatório não encontrado: ${file}`);
            }
        }
        
        for (const dir of requiredDirs) {
            if (!fs.existsSync(dir)) {
                this.error(`Diretório obrigatório não encontrado: ${dir}`);
            }
        }
    }

    async testPackageJson() {
        this.log('📦 Verificando package.json...');
        
        try {
            const packagePath = 'package.json';
            if (!fs.existsSync(packagePath)) {
                this.error('package.json não encontrado');
                return;
            }
            
            const packageContent = fs.readFileSync(packagePath, 'utf8');
            const packageJson = JSON.parse(packageContent);
            
            // Verifica dependências essenciais
            const requiredDeps = ['discord.js', 'mongoose', 'dotenv'];
            
            for (const dep of requiredDeps) {
                if (!packageJson.dependencies || !packageJson.dependencies[dep]) {
                    this.error(`Dependência obrigatória não encontrada: ${dep}`);
                }
            }
            
            // Verifica scripts
            if (!packageJson.scripts) {
                this.warn('Nenhum script definido no package.json');
            } else {
                if (!packageJson.scripts.start) {
                    this.warn('Script "start" não definido');
                }
            }
            
        } catch (error) {
            this.error(`Erro ao ler package.json: ${error.message}`);
        }
    }

    async testEnvironmentFile() {
        this.log('🔐 Verificando arquivos de ambiente...');
        
        if (!fs.existsSync('.env.example')) {
            this.error('.env.example não encontrado');
        }
        
        if (!fs.existsSync('.env')) {
            this.warn('.env não encontrado - necessário para execução');
        } else {
            try {
                const envContent = fs.readFileSync('.env', 'utf8');
                const requiredVars = ['DISCORD_TOKEN', 'CLIENT_ID', 'MONGODB_URI'];
                
                for (const varName of requiredVars) {
                    if (!envContent.includes(varName)) {
                        this.error(`Variável de ambiente obrigatória não encontrada: ${varName}`);
                    }
                }
            } catch (error) {
                this.error(`Erro ao ler .env: ${error.message}`);
            }
        }
    }

    async testModels() {
        this.log('🗄️ Verificando modelos...');
        
        const modelsDir = 'src/models';
        if (!fs.existsSync(modelsDir)) {
            this.error('Diretório de modelos não encontrado');
            return;
        }
        
        const expectedModels = [
            'Store.js',
            'Product.js',
            'User.js',
            'BotConfig.js',
            'Order.js',
            'StockItem.js'
        ];
        
        for (const model of expectedModels) {
            const modelPath = path.join(modelsDir, model);
            if (!fs.existsSync(modelPath)) {
                this.error(`Modelo não encontrado: ${model}`);
            } else {
                try {
                    const content = fs.readFileSync(modelPath, 'utf8');
                    if (!content.includes('mongoose.Schema')) {
                        this.error(`Modelo ${model} não parece ser um schema Mongoose válido`);
                    }
                    if (!content.includes('export default')) {
                        this.error(`Modelo ${model} não exporta corretamente`);
                    }
                } catch (error) {
                    this.error(`Erro ao ler modelo ${model}: ${error.message}`);
                }
            }
        }
    }

    async testCommands() {
        this.log('⚡ Verificando comandos...');
        
        const commandDirs = ['src/commands/general', 'src/commands/store'];
        
        for (const dir of commandDirs) {
            if (!fs.existsSync(dir)) {
                this.error(`Diretório de comandos não encontrado: ${dir}`);
                continue;
            }
            
            const files = fs.readdirSync(dir).filter(file => file.endsWith('.js'));
            
            if (files.length === 0) {
                this.warn(`Nenhum comando encontrado em: ${dir}`);
            }
            
            for (const file of files) {
                const filePath = path.join(dir, file);
                try {
                    const content = fs.readFileSync(filePath, 'utf8');
                    
                    if (!content.includes('SlashCommandBuilder')) {
                        this.error(`Comando ${file} não usa SlashCommandBuilder`);
                    }
                    
                    if (!content.includes('async execute')) {
                        this.error(`Comando ${file} não tem função execute`);
                    }
                    
                    if (!content.includes('export default')) {
                        this.error(`Comando ${file} não exporta corretamente`);
                    }
                    
                } catch (error) {
                    this.error(`Erro ao ler comando ${file}: ${error.message}`);
                }
            }
        }
    }

    async testHandlers() {
        this.log('🎯 Verificando handlers...');
        
        const handlersDir = 'src/handlers';
        if (!fs.existsSync(handlersDir)) {
            this.error('Diretório de handlers não encontrado');
            return;
        }
        
        const expectedHandlers = [
            'slashCommandHandler.js',
            'buttonHandler.js',
            'modalHandler.js',
            'selectMenuHandler.js',
            'autocompleteHandler.js'
        ];
        
        for (const handler of expectedHandlers) {
            const handlerPath = path.join(handlersDir, handler);
            if (!fs.existsSync(handlerPath)) {
                this.error(`Handler não encontrado: ${handler}`);
            }
        }
    }

    async testUtils() {
        this.log('🛠️ Verificando utilitários...');
        
        const utilsDir = 'src/utils';
        if (!fs.existsSync(utilsDir)) {
            this.error('Diretório de utilitários não encontrado');
            return;
        }
        
        const expectedUtils = [
            'logger.js',
            'commandLoader.js',
            'eventLoader.js',
            'storeManager.js'
        ];
        
        for (const util of expectedUtils) {
            const utilPath = path.join(utilsDir, util);
            if (!fs.existsSync(utilPath)) {
                this.error(`Utilitário não encontrado: ${util}`);
            }
        }
    }

    generateReport() {
        console.log('\n' + '='.repeat(50));
        console.log('📊 RELATÓRIO DE TESTES BÁSICOS');
        console.log('='.repeat(50));
        
        console.log(`✅ Testes concluídos`);
        console.log(`❌ Erros encontrados: ${this.errors.length}`);
        console.log(`⚠️ Avisos: ${this.warnings.length}`);
        
        if (this.errors.length > 0) {
            console.log('\n❌ ERROS:');
            this.errors.forEach((error, index) => {
                console.log(`${index + 1}. ${error}`);
            });
        }
        
        if (this.warnings.length > 0) {
            console.log('\n⚠️ AVISOS:');
            this.warnings.forEach((warning, index) => {
                console.log(`${index + 1}. ${warning}`);
            });
        }
        
        console.log('\n' + '='.repeat(50));
        
        if (this.errors.length === 0) {
            console.log('🎉 Estrutura básica do bot está correta!');
        } else {
            console.log('🔧 Corrija os erros antes de executar o bot.');
        }
    }

    getErrors() {
        return this.errors;
    }

    getWarnings() {
        return this.warnings;
    }
}

// Executa o teste
const test = new SimpleBotTest();
test.runBasicTests().catch(console.error);

export { SimpleBotTest };