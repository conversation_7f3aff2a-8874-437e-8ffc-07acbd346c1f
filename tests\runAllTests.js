import { TestRunner } from './testRunner.js';
import { logger } from '../src/utils/logger.js';
import fs from 'fs';
import path from 'path';

/**
 * Script principal para executar todos os testes do bot
 * e gerar relatório detalhado de erros
 */
class BotTestSuite {
    constructor() {
        this.runner = new TestRunner();
        this.startTime = null;
        this.endTime = null;
        this.errorReport = [];
    }

    async runCompleteTestSuite() {
        this.startTime = new Date();
        
        logger.info('🚀 INICIANDO SUITE COMPLETA DE TESTES DO BOT');
        logger.info('='.repeat(60));
        
        try {
            // Executa todos os testes
            await this.runner.runAllTests();
            
            // Coleta erros
            this.errorReport = this.runner.getErrors();
            
            this.endTime = new Date();
            
            // Gera relatórios
            await this.generateDetailedReport();
            await this.generateErrorList();
            
            logger.info('\n🎉 SUITE DE TESTES CONCLUÍDA');
            
        } catch (error) {
            logger.error('❌ Erro crítico na execução dos testes:', error);
            this.errorReport.push({
                test: 'Sistema de Testes',
                error: error.message,
                critical: true
            });
        }
        
        return this.errorReport;
    }

    async generateDetailedReport() {
        const duration = this.endTime - this.startTime;
        const reportPath = path.join(process.cwd(), 'tests', 'reports', 'detailed-report.md');
        
        // Cria diretório de relatórios se não existir
        const reportsDir = path.dirname(reportPath);
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }
        
        const report = this.buildDetailedReport(duration);
        
        fs.writeFileSync(reportPath, report, 'utf8');
        logger.info(`📄 Relatório detalhado salvo em: ${reportPath}`);
    }

    buildDetailedReport(duration) {
        const totalTests = this.runner.totalTests;
        const passedTests = this.runner.passedTests;
        const failedTests = this.errorReport.length;
        const successRate = ((passedTests / totalTests) * 100).toFixed(2);
        
        return `# Relatório de Testes do Bot Discord

## Resumo Executivo

- **Data/Hora**: ${this.startTime.toLocaleString('pt-BR')}
- **Duração**: ${(duration / 1000).toFixed(2)} segundos
- **Total de Testes**: ${totalTests}
- **Testes Aprovados**: ${passedTests}
- **Testes Falharam**: ${failedTests}
- **Taxa de Sucesso**: ${successRate}%

## Status Geral

${successRate >= 80 ? '✅ **APROVADO** - Sistema está funcionando adequadamente' : '❌ **REPROVADO** - Sistema requer atenção imediata'}

## Categorias Testadas

### 🗄️ Modelos do MongoDB
- Validação de schemas
- Métodos de instância
- Métodos estáticos
- Validações de dados

### ⚡ Comandos do Discord
- Estrutura de comandos slash
- Validação de parâmetros
- Funções de execução
- Permissões e validações

### 🎯 Handlers de Interação
- Manipulação de botões
- Processamento de modais
- Select menus
- Autocomplete
- Comandos slash

### 🛠️ Utilitários
- Sistema de logs
- Rate limiting
- Carregadores de comandos/eventos
- Gerenciador de lojas

## Detalhes dos Erros

${this.errorReport.length === 0 ? '🎉 **Nenhum erro encontrado!**' : this.formatErrorDetails()}

## Recomendações

${this.generateRecommendations()}

---

*Relatório gerado automaticamente pelo sistema de testes*
`;
    }

    formatErrorDetails() {
        return this.errorReport.map((error, index) => {
            return `### ${index + 1}. ${error.test}

**Erro**: \`${error.error}\`

**Severidade**: ${error.critical ? '🔴 Crítico' : '🟡 Moderado'}

---
`;
        }).join('\n');
    }

    generateRecommendations() {
        const recommendations = [];
        
        if (this.errorReport.length === 0) {
            recommendations.push('✅ Sistema está funcionando corretamente');
            recommendations.push('🔄 Execute testes regularmente para manter a qualidade');
        } else {
            recommendations.push('🔧 Corrija os erros identificados antes do deploy');
            
            const criticalErrors = this.errorReport.filter(e => e.critical);
            if (criticalErrors.length > 0) {
                recommendations.push('🚨 Priorize a correção dos erros críticos');
            }
            
            if (this.errorReport.length > 5) {
                recommendations.push('📋 Considere refatoração de código para melhorar qualidade');
            }
        }
        
        return recommendations.map(rec => `- ${rec}`).join('\n');
    }

    async generateErrorList() {
        if (this.errorReport.length === 0) {
            logger.info('✅ Nenhum erro para listar');
            return;
        }
        
        const errorListPath = path.join(process.cwd(), 'tests', 'reports', 'error-list.json');
        
        const errorData = {
            timestamp: this.startTime.toISOString(),
            totalErrors: this.errorReport.length,
            errors: this.errorReport.map((error, index) => ({
                id: index + 1,
                test: error.test,
                message: error.error,
                critical: error.critical || false,
                category: this.categorizeError(error.test)
            }))
        };
        
        fs.writeFileSync(errorListPath, JSON.stringify(errorData, null, 2), 'utf8');
        logger.info(`📋 Lista de erros salva em: ${errorListPath}`);
        
        // Exibe resumo dos erros no console
        this.displayErrorSummary();
    }

    categorizeError(testName) {
        if (testName.toLowerCase().includes('modelo')) return 'database';
        if (testName.toLowerCase().includes('comando')) return 'commands';
        if (testName.toLowerCase().includes('handler')) return 'handlers';
        if (testName.toLowerCase().includes('utilitário')) return 'utils';
        return 'system';
    }

    displayErrorSummary() {
        logger.info('\n📋 RESUMO DOS ERROS ENCONTRADOS:');
        logger.info('-'.repeat(50));
        
        this.errorReport.forEach((error, index) => {
            const severity = error.critical ? '🔴' : '🟡';
            logger.error(`${index + 1}. ${severity} ${error.test}`);
            logger.error(`   └─ ${error.error}`);
        });
        
        logger.info('-'.repeat(50));
    }
}

// Função para executar testes específicos
export async function runSpecificTests(categories = []) {
    const suite = new BotTestSuite();
    
    if (categories.length === 0) {
        return await suite.runCompleteTestSuite();
    }
    
    // Implementar execução de categorias específicas
    logger.info(`🎯 Executando testes específicos: ${categories.join(', ')}`);
    // TODO: Implementar lógica para categorias específicas
    
    return await suite.runCompleteTestSuite();
}

// Executa os testes se este arquivo for executado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
    const suite = new BotTestSuite();
    
    suite.runCompleteTestSuite()
        .then(errors => {
            if (errors.length > 0) {
                logger.error(`\n❌ Testes concluídos com ${errors.length} erro(s)`);
                process.exit(1);
            } else {
                logger.info('\n✅ Todos os testes passaram!');
                process.exit(0);
            }
        })
        .catch(error => {
            logger.error('❌ Erro crítico:', error);
            process.exit(1);
        });
}

export { BotTestSuite };