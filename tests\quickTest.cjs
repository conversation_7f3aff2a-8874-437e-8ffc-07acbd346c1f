const fs = require('fs');
const path = require('path');

console.log('🧪 Iniciando teste rápido do bot...');

const errors = [];
const warnings = [];

// Função para verificar se arquivo existe
function checkFile(filePath, description) {
    try {
        if (fs.existsSync(filePath)) {
            console.log(`✅ ${description}: ${filePath}`);
            return true;
        } else {
            const error = `❌ ${description} não encontrado: ${filePath}`;
            console.log(error);
            errors.push(error);
            return false;
        }
    } catch (err) {
        const error = `❌ Erro ao verificar ${description}: ${err.message}`;
        console.log(error);
        errors.push(error);
        return false;
    }
}

// Função para verificar sintaxe de arquivo JS
function checkSyntax(filePath, description) {
    try {
        require(filePath);
        console.log(`✅ Sintaxe OK: ${description}`);
        return true;
    } catch (err) {
        const error = `❌ Erro de sintaxe em ${description}: ${err.message}`;
        console.log(error);
        errors.push(error);
        return false;
    }
}

console.log('\n📁 Verificando arquivos principais...');
checkFile('./index.js', 'Arquivo principal');
checkFile('./package.json', 'Package.json');
checkFile('./.env.example', 'Arquivo de exemplo de ambiente');

console.log('\n🗄️ Verificando modelos...');
const modelsDir = './src/models';
if (fs.existsSync(modelsDir)) {
    const models = fs.readdirSync(modelsDir).filter(file => file.endsWith('.js'));
    models.forEach(model => {
        const modelPath = path.join(modelsDir, model);
        checkSyntax(modelPath, `Modelo ${model}`);
    });
} else {
    errors.push('❌ Diretório de modelos não encontrado');
}

console.log('\n⚡ Verificando comandos...');
const commandsDir = './src/commands';
if (fs.existsSync(commandsDir)) {
    const commands = fs.readdirSync(commandsDir).filter(file => file.endsWith('.js'));
    commands.forEach(command => {
        const commandPath = path.join(commandsDir, command);
        checkSyntax(commandPath, `Comando ${command}`);
    });
} else {
    errors.push('❌ Diretório de comandos não encontrado');
}

console.log('\n🎯 Verificando handlers...');
const handlersDir = './src/handlers';
if (fs.existsSync(handlersDir)) {
    const handlers = fs.readdirSync(handlersDir).filter(file => file.endsWith('.js'));
    handlers.forEach(handler => {
        const handlerPath = path.join(handlersDir, handler);
        checkSyntax(handlerPath, `Handler ${handler}`);
    });
} else {
    errors.push('❌ Diretório de handlers não encontrado');
}

console.log('\n🛠️ Verificando utilitários...');
const utilsDir = './src/utils';
if (fs.existsSync(utilsDir)) {
    const utils = fs.readdirSync(utilsDir).filter(file => file.endsWith('.js'));
    utils.forEach(util => {
        const utilPath = path.join(utilsDir, util);
        checkSyntax(utilPath, `Utilitário ${util}`);
    });
} else {
    errors.push('❌ Diretório de utilitários não encontrado');
}

console.log('\n🎪 Verificando eventos...');
const eventsDir = './src/events';
if (fs.existsSync(eventsDir)) {
    const events = fs.readdirSync(eventsDir).filter(file => file.endsWith('.js'));
    events.forEach(event => {
        const eventPath = path.join(eventsDir, event);
        checkSyntax(eventPath, `Evento ${event}`);
    });
} else {
    errors.push('❌ Diretório de eventos não encontrado');
}

// Verificar dependências
console.log('\n📦 Verificando dependências...');
try {
    const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
    const requiredDeps = ['discord.js', 'mongoose', 'dotenv'];
    
    requiredDeps.forEach(dep => {
        if (packageJson.dependencies && packageJson.dependencies[dep]) {
            console.log(`✅ Dependência encontrada: ${dep}`);
        } else {
            const error = `❌ Dependência obrigatória não encontrada: ${dep}`;
            console.log(error);
            errors.push(error);
        }
    });
} catch (err) {
    const error = `❌ Erro ao verificar dependências: ${err.message}`;
    console.log(error);
    errors.push(error);
}

// Relatório final
console.log('\n' + '='.repeat(50));
console.log('📊 RELATÓRIO FINAL DE TESTES');
console.log('='.repeat(50));
console.log(`✅ Testes concluídos`);
console.log(`❌ Erros encontrados: ${errors.length}`);
console.log(`⚠️ Avisos: ${warnings.length}`);

if (errors.length > 0) {
    console.log('\n❌ LISTA DE ERROS:');
    errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
    });
}

if (warnings.length > 0) {
    console.log('\n⚠️ LISTA DE AVISOS:');
    warnings.forEach((warning, index) => {
        console.log(`${index + 1}. ${warning}`);
    });
}

if (errors.length === 0) {
    console.log('\n🎉 Todos os testes passaram! O bot está funcionando corretamente.');
} else {
    console.log('\n🔧 Corrija os erros listados acima.');
}

console.log('\n✨ Teste concluído!');