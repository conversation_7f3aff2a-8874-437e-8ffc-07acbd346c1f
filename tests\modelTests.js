import Store from '../src/models/Store.js';
import Product from '../src/models/Product.js';
import User from '../src/models/User.js';
import BotConfig from '../src/models/BotConfig.js';
import Order from '../src/models/Order.js';
import StockItem from '../src/models/StockItem.js';
import { logger } from '../src/utils/logger.js';

/**
 * Testes para os modelos do MongoDB
 */
export async function testModels() {
    logger.info('🧪 Iniciando testes dos modelos...');
    
    await testStoreModel();
    await testProductModel();
    await testUserModel();
    await testBotConfigModel();
    await testOrderModel();
    await testStockItemModel();
    
    logger.info('✅ Todos os testes de modelos concluídos');
}

async function testStoreModel() {
    logger.info('🏪 Testando modelo Store...');
    
    // Teste de criação de loja
    const testStore = new Store({
        name: 'Loja Teste',
        description: 'Uma loja de teste para validação',
        banner: 'https://example.com/banner.jpg',
        color: '#FF0000',
        guildId: '123456789',
        channelId: '987654321',
        createdBy: '111222333'
    });
    
    // Valida campos obrigatórios
    if (!testStore.name) throw new Error('Nome da loja é obrigatório');
    if (!testStore.description) throw new Error('Descrição da loja é obrigatória');
    if (!testStore.banner) throw new Error('Banner da loja é obrigatório');
    if (!testStore.color) throw new Error('Cor da loja é obrigatória');
    if (!testStore.guildId) throw new Error('Guild ID é obrigatório');
    if (!testStore.channelId) throw new Error('Channel ID é obrigatório');
    if (!testStore.createdBy) throw new Error('Created By é obrigatório');
    
    // Testa métodos
    const colorInt = testStore.getColorAsInt();
    if (typeof colorInt !== 'number') throw new Error('getColorAsInt deve retornar um número');
    
    const isAllowed = testStore.isUserAllowed('111222333', []);
    if (typeof isAllowed !== 'boolean') throw new Error('isUserAllowed deve retornar um boolean');
    
    logger.info('✅ Modelo Store validado');
}

async function testProductModel() {
    logger.info('📦 Testando modelo Product...');
    
    const testProduct = new Product({
        name: 'Produto Teste',
        description: 'Um produto de teste',
        price: 29.99,
        stock: 10,
        category: 'digital',
        storeId: '507f1f77bcf86cd799439011',
        createdBy: '111222333'
    });
    
    // Valida campos obrigatórios
    if (!testProduct.name) throw new Error('Nome do produto é obrigatório');
    if (!testProduct.description) throw new Error('Descrição do produto é obrigatória');
    if (testProduct.price === undefined) throw new Error('Preço do produto é obrigatório');
    if (testProduct.stock === undefined) throw new Error('Estoque do produto é obrigatório');
    if (!testProduct.category) throw new Error('Categoria do produto é obrigatória');
    if (!testProduct.storeId) throw new Error('Store ID é obrigatório');
    if (!testProduct.createdBy) throw new Error('Created By é obrigatório');
    
    // Testa métodos
    testProduct.updateStock(-5);
    if (testProduct.stock !== 5) throw new Error('updateStock não funcionou corretamente');
    
    testProduct.incrementViews();
    if (testProduct.views !== 1) throw new Error('incrementViews não funcionou corretamente');
    
    testProduct.recordSale(2);
    if (testProduct.totalSold !== 2) throw new Error('recordSale não funcionou corretamente');
    
    logger.info('✅ Modelo Product validado');
}

async function testUserModel() {
    logger.info('👤 Testando modelo User...');
    
    const testUser = new User({
        discordId: '123456789',
        username: 'TestUser',
        discriminator: '1234'
    });
    
    // Valida campos obrigatórios
    if (!testUser.discordId) throw new Error('Discord ID é obrigatório');
    if (!testUser.username) throw new Error('Username é obrigatório');
    if (!testUser.discriminator) throw new Error('Discriminator é obrigatório');
    
    // Testa métodos
    testUser.addBalance(100);
    if (testUser.balance !== 100) throw new Error('addBalance não funcionou corretamente');
    
    const success = testUser.deductBalance(50);
    if (!success || testUser.balance !== 50) throw new Error('deductBalance não funcionou corretamente');
    
    const failed = testUser.deductBalance(100);
    if (failed) throw new Error('deductBalance deveria falhar com saldo insuficiente');
    
    testUser.updateLastSeen();
    if (!testUser.lastSeen) throw new Error('updateLastSeen não funcionou corretamente');
    
    logger.info('✅ Modelo User validado');
}

async function testBotConfigModel() {
    logger.info('⚙️ Testando modelo BotConfig...');
    
    const testConfig = new BotConfig({
        guildId: '123456789',
        adminLogChannelId: '987654321',
        publicLogChannelId: '111222333'
    });
    
    // Valida campos obrigatórios
    if (!testConfig.guildId) throw new Error('Guild ID é obrigatório');
    
    // Testa valores padrão
    if (testConfig.rateLimitEnabled !== true) throw new Error('Rate limit deveria estar habilitado por padrão');
    if (testConfig.maxCommandsPerMinute !== 10) throw new Error('Limite padrão de comandos por minuto deveria ser 10');
    
    logger.info('✅ Modelo BotConfig validado');
}

async function testOrderModel() {
    logger.info('🛒 Testando modelo Order...');
    
    const testOrder = new Order({
        userId: '123456789',
        productId: '507f1f77bcf86cd799439011',
        storeId: '507f1f77bcf86cd799439012',
        quantity: 2,
        unitPrice: 29.99,
        totalPrice: 59.98
    });
    
    // Valida campos obrigatórios
    if (!testOrder.userId) throw new Error('User ID é obrigatório');
    if (!testOrder.productId) throw new Error('Product ID é obrigatório');
    if (!testOrder.storeId) throw new Error('Store ID é obrigatório');
    if (testOrder.quantity === undefined) throw new Error('Quantidade é obrigatória');
    if (testOrder.unitPrice === undefined) throw new Error('Preço unitário é obrigatório');
    if (testOrder.totalPrice === undefined) throw new Error('Preço total é obrigatório');
    
    // Testa valores padrão
    if (testOrder.status !== 'pending') throw new Error('Status padrão deveria ser pending');
    
    logger.info('✅ Modelo Order validado');
}

async function testStockItemModel() {
    logger.info('📋 Testando modelo StockItem...');
    
    const testStockItem = new StockItem({
        productId: '507f1f77bcf86cd799439011',
        content: 'Conteúdo do item de estoque',
        createdBy: '123456789'
    });
    
    // Valida campos obrigatórios
    if (!testStockItem.productId) throw new Error('Product ID é obrigatório');
    if (!testStockItem.content) throw new Error('Conteúdo é obrigatório');
    if (!testStockItem.createdBy) throw new Error('Created By é obrigatório');
    
    // Testa valores padrão
    if (testStockItem.isUsed !== false) throw new Error('isUsed deveria ser false por padrão');
    if (testStockItem.status !== 'available') throw new Error('Status padrão deveria ser available');
    
    logger.info('✅ Modelo StockItem validado');
}