import { logger } from '../src/utils/logger.js';
import fs from 'fs';
import path from 'path';

/**
 * Testes para os handlers de interação
 */
export async function testHandlers() {
    logger.info('🧪 Iniciando testes dos handlers...');
    
    await testSlashCommandHandler();
    await testButtonHandler();
    await testModalHandler();
    await testSelectMenuHandler();
    await testAutocompleteHandler();
    
    logger.info('✅ Todos os testes de handlers concluídos');
}

async function testSlashCommandHandler() {
    logger.info('⚡ Testando slash command handler...');
    
    const handlerPath = path.join(process.cwd(), 'src', 'handlers', 'slashCommandHandler.js');
    
    if (!fs.existsSync(handlerPath)) {
        throw new Error('Slash command handler não encontrado');
    }
    
    const handler = await import(`file://${handlerPath}`);
    
    if (!handler.handleSlashCommand) {
        throw new Error('Handler deve exportar função handleSlashCommand');
    }
    
    if (typeof handler.handleSlashCommand !== 'function') {
        throw new Error('handleSlashCommand deve ser uma função');
    }
    
    // Teste com mock interaction
    const mockInteraction = {
        commandName: 'ping',
        client: {
            commands: new Map()
        },
        reply: async () => {}
    };
    
    try {
        await handler.handleSlashCommand(mockInteraction);
    } catch (error) {
        // Esperado falhar por comando não encontrado
        if (!error.message.includes('comando') && !error.message.includes('command')) {
            throw error;
        }
    }
    
    logger.info('✅ Slash command handler validado');
}

async function testButtonHandler() {
    logger.info('🔘 Testando button handler...');
    
    const handlerPath = path.join(process.cwd(), 'src', 'handlers', 'buttonHandler.js');
    
    if (!fs.existsSync(handlerPath)) {
        throw new Error('Button handler não encontrado');
    }
    
    const handler = await import(`file://${handlerPath}`);
    
    if (!handler.handleButton) {
        throw new Error('Handler deve exportar função handleButton');
    }
    
    if (typeof handler.handleButton !== 'function') {
        throw new Error('handleButton deve ser uma função');
    }
    
    // Teste com mock interaction
    const mockInteraction = {
        customId: 'test_button',
        member: {
            permissions: {
                has: () => true
            }
        },
        guild: { id: '123456789' },
        reply: async () => {}
    };
    
    try {
        await handler.handleButton(mockInteraction);
    } catch (error) {
        // Esperado falhar por botão não reconhecido
        if (!error.message.includes('reconhecido') && !error.message.includes('recognized')) {
            throw error;
        }
    }
    
    logger.info('✅ Button handler validado');
}

async function testModalHandler() {
    logger.info('📝 Testando modal handler...');
    
    const handlerPath = path.join(process.cwd(), 'src', 'handlers', 'modalHandler.js');
    
    if (!fs.existsSync(handlerPath)) {
        throw new Error('Modal handler não encontrado');
    }
    
    const handler = await import(`file://${handlerPath}`);
    
    if (!handler.handleModal) {
        throw new Error('Handler deve exportar função handleModal');
    }
    
    if (typeof handler.handleModal !== 'function') {
        throw new Error('handleModal deve ser uma função');
    }
    
    // Teste com mock interaction
    const mockInteraction = {
        customId: 'test_modal',
        fields: {
            getTextInputValue: (id) => 'test value'
        },
        member: {
            permissions: {
                has: () => true
            }
        },
        guild: {
            id: '123456789',
            channels: {
                create: async () => ({ id: '987654321' })
            }
        },
        reply: async () => {}
    };
    
    try {
        await handler.handleModal(mockInteraction);
    } catch (error) {
        // Esperado falhar por modal não reconhecido ou validação
        if (!error.message.includes('reconhecido') && 
            !error.message.includes('recognized') && 
            !error.message.includes('URL') &&
            !error.message.includes('cor')) {
            throw error;
        }
    }
    
    logger.info('✅ Modal handler validado');
}

async function testSelectMenuHandler() {
    logger.info('📋 Testando select menu handler...');
    
    const handlerPath = path.join(process.cwd(), 'src', 'handlers', 'selectMenuHandler.js');
    
    if (!fs.existsSync(handlerPath)) {
        throw new Error('Select menu handler não encontrado');
    }
    
    const handler = await import(`file://${handlerPath}`);
    
    if (!handler.handleSelectMenu) {
        throw new Error('Handler deve exportar função handleSelectMenu');
    }
    
    if (typeof handler.handleSelectMenu !== 'function') {
        throw new Error('handleSelectMenu deve ser uma função');
    }
    
    // Teste com mock interaction
    const mockInteraction = {
        customId: 'test_select',
        values: ['test_value'],
        member: {
            permissions: {
                has: () => true
            }
        },
        guild: { id: '123456789' },
        reply: async () => {},
        update: async () => {}
    };
    
    try {
        await handler.handleSelectMenu(mockInteraction);
    } catch (error) {
        // Esperado falhar por select menu não reconhecido
        if (!error.message.includes('reconhecido') && !error.message.includes('recognized')) {
            throw error;
        }
    }
    
    logger.info('✅ Select menu handler validado');
}

async function testAutocompleteHandler() {
    logger.info('🔍 Testando autocomplete handler...');
    
    const handlerPath = path.join(process.cwd(), 'src', 'handlers', 'autocompleteHandler.js');
    
    if (!fs.existsSync(handlerPath)) {
        throw new Error('Autocomplete handler não encontrado');
    }
    
    const handler = await import(`file://${handlerPath}`);
    
    if (!handler.handleAutocomplete) {
        throw new Error('Handler deve exportar função handleAutocomplete');
    }
    
    if (typeof handler.handleAutocomplete !== 'function') {
        throw new Error('handleAutocomplete deve ser uma função');
    }
    
    // Teste com mock interaction
    const mockInteraction = {
        commandName: 'test_command',
        options: {
            getFocused: () => ({ name: 'test', value: 'test' })
        },
        respond: async () => {}
    };
    
    try {
        await handler.handleAutocomplete(mockInteraction);
    } catch (error) {
        // Esperado falhar por comando não reconhecido
        if (!error.message.includes('reconhecido') && !error.message.includes('recognized')) {
            throw error;
        }
    }
    
    logger.info('✅ Autocomplete handler validado');
}

/**
 * Testa a estrutura geral dos handlers
 */
export async function testHandlerStructure() {
    logger.info('🏗️ Testando estrutura dos handlers...');
    
    const handlersPath = path.join(process.cwd(), 'src', 'handlers');
    const expectedHandlers = [
        'slashCommandHandler.js',
        'buttonHandler.js',
        'modalHandler.js',
        'selectMenuHandler.js',
        'autocompleteHandler.js'
    ];
    
    for (const handlerFile of expectedHandlers) {
        const handlerPath = path.join(handlersPath, handlerFile);
        
        if (!fs.existsSync(handlerPath)) {
            throw new Error(`Handler obrigatório não encontrado: ${handlerFile}`);
        }
        
        const handler = await import(`file://${handlerPath}`);
        
        // Verifica se o handler exporta pelo menos uma função
        const exportedFunctions = Object.values(handler).filter(exp => typeof exp === 'function');
        
        if (exportedFunctions.length === 0) {
            throw new Error(`Handler ${handlerFile} não exporta nenhuma função`);
        }
    }
    
    logger.info('✅ Estrutura dos handlers validada');
}