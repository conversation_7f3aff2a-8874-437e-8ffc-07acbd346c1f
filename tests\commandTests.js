import { SlashCommandBuilder } from 'discord.js';
import { logger } from '../src/utils/logger.js';
import fs from 'fs';
import path from 'path';

/**
 * Testes para os comandos do Discord
 */
export async function testCommands() {
    logger.info('🧪 Iniciando testes dos comandos...');
    
    await testGeneralCommands();
    await testStoreCommands();
    
    logger.info('✅ Todos os testes de comandos concluídos');
}

async function testGeneralCommands() {
    logger.info('🔧 Testando comandos gerais...');
    
    const generalCommandsPath = path.join(process.cwd(), 'src', 'commands', 'general');
    const commandFiles = fs.readdirSync(generalCommandsPath).filter(file => file.endsWith('.js'));
    
    for (const file of commandFiles) {
        const commandPath = path.join(generalCommandsPath, file);
        const command = await import(`file://${commandPath}`);
        
        await testCommand(command.default, file);
    }
    
    logger.info('✅ Comandos gerais validados');
}

async function testStoreCommands() {
    logger.info('🏪 Testando comandos da loja...');
    
    const storeCommandsPath = path.join(process.cwd(), 'src', 'commands', 'store');
    const commandFiles = fs.readdirSync(storeCommandsPath).filter(file => file.endsWith('.js'));
    
    for (const file of commandFiles) {
        const commandPath = path.join(storeCommandsPath, file);
        const command = await import(`file://${commandPath}`);
        
        await testCommand(command.default, file);
    }
    
    logger.info('✅ Comandos da loja validados');
}

async function testCommand(command, fileName) {
    logger.info(`🔍 Testando comando: ${fileName}`);
    
    // Verifica se o comando tem a estrutura correta
    if (!command) {
        throw new Error(`Comando ${fileName} não foi exportado corretamente`);
    }
    
    if (!command.data) {
        throw new Error(`Comando ${fileName} não possui propriedade 'data'`);
    }
    
    if (!command.execute) {
        throw new Error(`Comando ${fileName} não possui função 'execute'`);
    }
    
    // Verifica se data é uma instância de SlashCommandBuilder
    if (!(command.data instanceof SlashCommandBuilder)) {
        throw new Error(`Comando ${fileName} - 'data' deve ser uma instância de SlashCommandBuilder`);
    }
    
    // Verifica se execute é uma função
    if (typeof command.execute !== 'function') {
        throw new Error(`Comando ${fileName} - 'execute' deve ser uma função`);
    }
    
    // Verifica propriedades básicas do comando
    const commandData = command.data.toJSON();
    
    if (!commandData.name) {
        throw new Error(`Comando ${fileName} não possui nome`);
    }
    
    if (!commandData.description) {
        throw new Error(`Comando ${fileName} não possui descrição`);
    }
    
    // Verifica se o nome do comando é válido
    if (!/^[a-z0-9_-]{1,32}$/.test(commandData.name)) {
        throw new Error(`Comando ${fileName} possui nome inválido: ${commandData.name}`);
    }
    
    // Verifica se a descrição tem tamanho adequado
    if (commandData.description.length < 1 || commandData.description.length > 100) {
        throw new Error(`Comando ${fileName} possui descrição com tamanho inválido`);
    }
    
    // Testes específicos por tipo de comando
    await testSpecificCommand(command, fileName, commandData);
    
    logger.info(`✅ Comando ${fileName} validado`);
}

async function testSpecificCommand(command, fileName, commandData) {
    switch (commandData.name) {
        case 'ping':
            await testPingCommand(command);
            break;
        case 'info':
            await testInfoCommand(command);
            break;
        case 'help':
            await testHelpCommand(command);
            break;
        case 'configbot':
            await testConfigBotCommand(command);
            break;
        case 'criar-loja':
            await testCriarLojaCommand(command);
            break;
        case 'deletar-loja':
            await testDeletarLojaCommand(command);
            break;
        case 'editar-loja':
            await testEditarLojaCommand(command);
            break;
        case 'criar-produto':
            await testCriarProdutoCommand(command);
            break;
        case 'editar-produtos':
            await testEditarProdutosCommand(command);
            break;
        case 'criar-estoque':
            await testCriarEstoqueCommand(command);
            break;
        case 'visualizar-estoque':
            await testVisualizarEstoqueCommand(command);
            break;
        case 'editar-estoque':
            await testEditarEstoqueCommand(command);
            break;
        case 'deletar-estoque':
            await testDeletarEstoqueCommand(command);
            break;
        case 'reenviar-lojas':
            await testReenviarLojasCommand(command);
            break;
        default:
            logger.warn(`⚠️ Teste específico não implementado para comando: ${commandData.name}`);
    }
}

// Testes específicos para cada comando
async function testPingCommand(command) {
    // Mock de interaction para teste
    const mockInteraction = {
        reply: async (options) => {
            if (!options.content && !options.embeds) {
                throw new Error('Ping command deve responder com conteúdo ou embed');
            }
        },
        client: {
            ws: { ping: 50 }
        }
    };
    
    // Simula execução do comando
    try {
        await command.execute(mockInteraction);
    } catch (error) {
        if (!error.message.includes('reply')) {
            throw error;
        }
    }
}

async function testInfoCommand(command) {
    const mockInteraction = {
        reply: async (options) => {
            if (!options.embeds || !Array.isArray(options.embeds)) {
                throw new Error('Info command deve responder com embeds');
            }
        },
        client: {
            user: { tag: 'TestBot#1234', displayAvatarURL: () => 'https://example.com/avatar.png' },
            guilds: { cache: { size: 1 } },
            users: { cache: { size: 10 } },
            uptime: 60000
        }
    };
    
    try {
        await command.execute(mockInteraction);
    } catch (error) {
        if (!error.message.includes('reply')) {
            throw error;
        }
    }
}

async function testHelpCommand(command) {
    const mockInteraction = {
        reply: async (options) => {
            if (!options.embeds || !Array.isArray(options.embeds)) {
                throw new Error('Help command deve responder com embeds');
            }
        },
        client: {
            user: { displayAvatarURL: () => 'https://example.com/avatar.png' }
        }
    };
    
    try {
        await command.execute(mockInteraction);
    } catch (error) {
        if (!error.message.includes('reply')) {
            throw error;
        }
    }
}

async function testConfigBotCommand(command) {
    const mockInteraction = {
        member: {
            permissions: {
                has: (permission) => permission === 'Administrator'
            }
        },
        guild: { id: '123456789' },
        reply: async () => {},
        client: {
            user: { displayAvatarURL: () => 'https://example.com/avatar.png' }
        }
    };
    
    try {
        await command.execute(mockInteraction);
    } catch (error) {
        // Esperado falhar por não ter banco de dados conectado
        if (!error.message.includes('BotConfig') && !error.message.includes('reply')) {
            throw error;
        }
    }
}

async function testCriarLojaCommand(command) {
    const mockInteraction = {
        member: {
            permissions: {
                has: (permission) => permission === 'Administrator'
            }
        },
        guild: {
            members: {
                me: {
                    permissions: {
                        has: () => true
                    }
                }
            }
        },
        showModal: async () => {}
    };
    
    try {
        await command.execute(mockInteraction);
    } catch (error) {
        if (!error.message.includes('showModal')) {
            throw error;
        }
    }
}

// Implementações similares para outros comandos da loja
async function testDeletarLojaCommand(command) {
    // Teste básico de estrutura
    if (typeof command.execute !== 'function') {
        throw new Error('Deletar loja command deve ter função execute');
    }
}

async function testEditarLojaCommand(command) {
    if (typeof command.execute !== 'function') {
        throw new Error('Editar loja command deve ter função execute');
    }
}

async function testCriarProdutoCommand(command) {
    if (typeof command.execute !== 'function') {
        throw new Error('Criar produto command deve ter função execute');
    }
}

async function testEditarProdutosCommand(command) {
    if (typeof command.execute !== 'function') {
        throw new Error('Editar produtos command deve ter função execute');
    }
}

async function testCriarEstoqueCommand(command) {
    if (typeof command.execute !== 'function') {
        throw new Error('Criar estoque command deve ter função execute');
    }
}

async function testVisualizarEstoqueCommand(command) {
    if (typeof command.execute !== 'function') {
        throw new Error('Visualizar estoque command deve ter função execute');
    }
}

async function testEditarEstoqueCommand(command) {
    if (typeof command.execute !== 'function') {
        throw new Error('Editar estoque command deve ter função execute');
    }
}

async function testDeletarEstoqueCommand(command) {
    if (typeof command.execute !== 'function') {
        throw new Error('Deletar estoque command deve ter função execute');
    }
}

async function testReenviarLojasCommand(command) {
    if (typeof command.execute !== 'function') {
        throw new Error('Reenviar lojas command deve ter função execute');
    }
}