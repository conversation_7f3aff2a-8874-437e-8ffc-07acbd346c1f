import { Client, GatewayIntentBits } from 'discord.js';
import { config } from 'dotenv';
import { connectDatabase } from '../src/database/connection.js';
import { logger } from '../src/utils/logger.js';

// Carrega variáveis de ambiente
config();

/**
 * Runner principal para executar todos os testes do bot
 */
class TestRunner {
    constructor() {
        this.client = null;
        this.errors = [];
        this.passedTests = 0;
        this.totalTests = 0;
    }

    async initialize() {
        try {
            logger.info('🧪 Iniciando testes do bot...');
            
            // Conecta ao banco de dados
            await connectDatabase();
            
            // Cria cliente Discord para testes
            this.client = new Client({
                intents: [
                    GatewayIntentBits.Guilds,
                    GatewayIntentBits.GuildMessages,
                    GatewayIntentBits.MessageContent,
                    GatewayIntentBits.GuildMembers
                ]
            });
            
            logger.info('✅ Inicialização dos testes concluída');
        } catch (error) {
            logger.error('❌ Erro na inicialização dos testes:', error);
            throw error;
        }
    }

    async runTest(testName, testFunction) {
        this.totalTests++;
        try {
            logger.info(`🔍 Executando teste: ${testName}`);
            await testFunction();
            this.passedTests++;
            logger.info(`✅ Teste passou: ${testName}`);
        } catch (error) {
            this.errors.push({ test: testName, error: error.message });
            logger.error(`❌ Teste falhou: ${testName} - ${error.message}`);
        }
    }

    async runAllTests() {
        await this.initialize();
        
        // Importa e executa todos os testes
        const { testModels } = await import('./modelTests.js');
        const { testCommands } = await import('./commandTests.js');
        const { testHandlers } = await import('./handlerTests.js');
        const { testUtils } = await import('./utilTests.js');
        
        // Executa testes de modelos
        await this.runTest('Modelos do MongoDB', testModels);
        
        // Executa testes de comandos
        await this.runTest('Comandos do Discord', testCommands);
        
        // Executa testes de handlers
        await this.runTest('Handlers de Interação', testHandlers);
        
        // Executa testes de utilitários
        await this.runTest('Utilitários', testUtils);
        
        this.generateReport();
    }

    generateReport() {
        logger.info('\n📊 RELATÓRIO DE TESTES');
        logger.info('='.repeat(50));
        logger.info(`Total de testes: ${this.totalTests}`);
        logger.info(`Testes aprovados: ${this.passedTests}`);
        logger.info(`Testes falharam: ${this.errors.length}`);
        logger.info(`Taxa de sucesso: ${((this.passedTests / this.totalTests) * 100).toFixed(2)}%`);
        
        if (this.errors.length > 0) {
            logger.info('\n❌ ERROS ENCONTRADOS:');
            this.errors.forEach((error, index) => {
                logger.error(`${index + 1}. ${error.test}: ${error.error}`);
            });
        }
        
        logger.info('='.repeat(50));
    }

    getErrors() {
        return this.errors;
    }
}

export { TestRunner };

// Executa os testes se este arquivo for executado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
    const runner = new TestRunner();
    runner.runAllTests().catch(console.error);
}