import { logger } from '../src/utils/logger.js';
import { RateLimiter } from '../src/utils/rateLimiter.js';
import { BotLogger } from '../src/utils/botLogger.js';
import fs from 'fs';
import path from 'path';

/**
 * Testes para os utilitários do bot
 */
export async function testUtils() {
    logger.info('🧪 Iniciando testes dos utilitários...');
    
    await testLogger();
    await testRateLimiter();
    await testBotLogger();
    await testCommandLoader();
    await testEventLoader();
    await testStoreManager();
    
    logger.info('✅ Todos os testes de utilitários concluídos');
}

async function testLogger() {
    logger.info('📝 Testando logger...');
    
    // Verifica se o logger tem os métodos necessários
    const requiredMethods = ['info', 'warn', 'error', 'debug'];
    
    for (const method of requiredMethods) {
        if (typeof logger[method] !== 'function') {
            throw new Error(`Logger deve ter método ${method}`);
        }
    }
    
    // Testa se os métodos funcionam sem erro
    try {
        logger.info('Teste de log info');
        logger.warn('Teste de log warn');
        logger.error('Teste de log error');
        logger.debug('Teste de log debug');
    } catch (error) {
        throw new Error(`Erro ao executar métodos do logger: ${error.message}`);
    }
    
    logger.info('✅ Logger validado');
}

async function testRateLimiter() {
    logger.info('⏱️ Testando rate limiter...');
    
    // Cria uma instância do rate limiter
    const rateLimiter = new RateLimiter();
    
    // Verifica se tem os métodos necessários
    const requiredMethods = ['checkLimit', 'addAttempt', 'resetAttempts', 'cleanupOldAttempts'];
    
    for (const method of requiredMethods) {
        if (typeof rateLimiter[method] !== 'function') {
            throw new Error(`RateLimiter deve ter método ${method}`);
        }
    }
    
    // Testa funcionalidade básica
    const userId = 'test_user_123';
    const guildId = 'test_guild_123';
    
    // Primeiro check deve passar
    const firstCheck = await rateLimiter.checkLimit(userId, guildId);
    if (firstCheck.limited) {
        throw new Error('Primeiro check não deveria estar limitado');
    }
    
    // Adiciona tentativa
    rateLimiter.addAttempt(userId, guildId);
    
    // Verifica se a tentativa foi registrada
    if (!rateLimiter.attempts.has(userId)) {
        throw new Error('Tentativa não foi registrada');
    }
    
    // Reset de tentativas
    rateLimiter.resetAttempts(userId);
    
    // Verifica se foi resetado
    if (rateLimiter.attempts.has(userId)) {
        throw new Error('Tentativas não foram resetadas');
    }
    
    logger.info('✅ Rate limiter validado');
}

async function testBotLogger() {
    logger.info('🤖 Testando bot logger...');
    
    // Verifica se BotLogger tem os métodos necessários
    const requiredMethods = ['logCommand', 'logError', 'logUserAction', 'logAdminAction'];
    
    for (const method of requiredMethods) {
        if (typeof BotLogger[method] !== 'function') {
            throw new Error(`BotLogger deve ter método ${method}`);
        }
    }
    
    // Mock de dados para teste
    const mockInteraction = {
        user: { id: '123456789', tag: 'TestUser#1234' },
        guild: { id: '987654321', name: 'Test Guild' },
        commandName: 'test-command'
    };
    
    // Testa métodos sem erro
    try {
        await BotLogger.logCommand(mockInteraction, 'success');
        await BotLogger.logError('Test error', mockInteraction);
        await BotLogger.logUserAction(mockInteraction.user.id, 'test_action', { test: 'data' });
        await BotLogger.logAdminAction(mockInteraction.user.id, 'test_admin_action', { test: 'data' });
    } catch (error) {
        // Esperado falhar por não ter configuração do banco
        if (!error.message.includes('BotConfig') && !error.message.includes('database')) {
            throw error;
        }
    }
    
    logger.info('✅ Bot logger validado');
}

async function testCommandLoader() {
    logger.info('⚡ Testando command loader...');
    
    const loaderPath = path.join(process.cwd(), 'src', 'utils', 'commandLoader.js');
    
    if (!fs.existsSync(loaderPath)) {
        throw new Error('Command loader não encontrado');
    }
    
    const loader = await import(`file://${loaderPath}`);
    
    if (!loader.loadCommands) {
        throw new Error('Command loader deve exportar função loadCommands');
    }
    
    if (typeof loader.loadCommands !== 'function') {
        throw new Error('loadCommands deve ser uma função');
    }
    
    // Mock de client para teste
    const mockClient = {
        commands: new Map()
    };
    
    try {
        await loader.loadCommands(mockClient);
        
        // Verifica se comandos foram carregados
        if (mockClient.commands.size === 0) {
            throw new Error('Nenhum comando foi carregado');
        }
        
    } catch (error) {
        // Pode falhar por problemas de importação, mas estrutura deve estar correta
        if (!error.message.includes('import') && !error.message.includes('ENOENT')) {
            throw error;
        }
    }
    
    logger.info('✅ Command loader validado');
}

async function testEventLoader() {
    logger.info('🎯 Testando event loader...');
    
    const loaderPath = path.join(process.cwd(), 'src', 'utils', 'eventLoader.js');
    
    if (!fs.existsSync(loaderPath)) {
        throw new Error('Event loader não encontrado');
    }
    
    const loader = await import(`file://${loaderPath}`);
    
    if (!loader.loadEvents) {
        throw new Error('Event loader deve exportar função loadEvents');
    }
    
    if (typeof loader.loadEvents !== 'function') {
        throw new Error('loadEvents deve ser uma função');
    }
    
    // Mock de client para teste
    const mockClient = {
        on: () => {},
        once: () => {}
    };
    
    try {
        await loader.loadEvents(mockClient);
    } catch (error) {
        // Pode falhar por problemas de importação, mas estrutura deve estar correta
        if (!error.message.includes('import') && !error.message.includes('ENOENT')) {
            throw error;
        }
    }
    
    logger.info('✅ Event loader validado');
}

async function testStoreManager() {
    logger.info('🏪 Testando store manager...');
    
    const managerPath = path.join(process.cwd(), 'src', 'utils', 'storeManager.js');
    
    if (!fs.existsSync(managerPath)) {
        throw new Error('Store manager não encontrado');
    }
    
    const manager = await import(`file://${managerPath}`);
    
    // Verifica se exporta as funções necessárias
    const expectedFunctions = [
        'createStoreChannel',
        'updateStoreMessage',
        'deleteStoreChannel',
        'validateStoreData',
        'getStoreProducts'
    ];
    
    for (const funcName of expectedFunctions) {
        if (!manager[funcName]) {
            logger.warn(`⚠️ Função ${funcName} não encontrada no store manager`);
        } else if (typeof manager[funcName] !== 'function') {
            throw new Error(`${funcName} deve ser uma função`);
        }
    }
    
    // Testa validação de dados da loja
    if (manager.validateStoreData) {
        try {
            const validData = {
                name: 'Loja Teste',
                description: 'Uma loja de teste',
                banner: 'https://example.com/banner.jpg',
                color: '#FF0000'
            };
            
            const result = manager.validateStoreData(validData);
            if (!result.valid) {
                throw new Error('Dados válidos foram rejeitados');
            }
            
        } catch (error) {
            if (!error.message.includes('rejeitados')) {
                throw error;
            }
        }
    }
    
    logger.info('✅ Store manager validado');
}

/**
 * Testa a estrutura geral dos utilitários
 */
export async function testUtilStructure() {
    logger.info('🏗️ Testando estrutura dos utilitários...');
    
    const utilsPath = path.join(process.cwd(), 'src', 'utils');
    const expectedUtils = [
        'logger.js',
        'rateLimiter.js',
        'botLogger.js',
        'commandLoader.js',
        'eventLoader.js',
        'storeManager.js'
    ];
    
    for (const utilFile of expectedUtils) {
        const utilPath = path.join(utilsPath, utilFile);
        
        if (!fs.existsSync(utilPath)) {
            throw new Error(`Utilitário obrigatório não encontrado: ${utilFile}`);
        }
        
        const util = await import(`file://${utilPath}`);
        
        // Verifica se o utilitário exporta algo
        const exports = Object.keys(util);
        
        if (exports.length === 0) {
            throw new Error(`Utilitário ${utilFile} não exporta nada`);
        }
    }
    
    logger.info('✅ Estrutura dos utilitários validada');
}